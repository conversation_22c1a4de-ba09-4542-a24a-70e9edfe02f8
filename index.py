import React, { useState, useEffect, useRef } from 'react';
import { Play, Square, Send, Monitor, AlertCircle, CheckCircle, Clock, Loader } from 'lucide-react';

const API_BASE_URL = 'http://localhost:8000/api';

const BrowserAgentDashboard = () => {
  const [sessions, setSessions] = useState([]);
  const [activeSessions, setActiveSessions] = useState([]);
  const [newInstructions, setNewInstructions] = useState('');
  const [startUrl, setStartUrl] = useState('https://google.com');
  const [selectedSession, setSelectedSession] = useState(null);
  const [sessionDetails, setSessionDetails] = useState(null);
  const [isStarting, setIsStarting] = useState(false);
  const [additionalCommand, setAdditionalCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [connectionError, setConnectionError] = useState('');
  
  const wsRef = useRef(null);
  const pollingRef = useRef(null);

  // Fetch all sessions
  const fetchSessions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions`);
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions || []);
        setConnectionError('');
      } else {
        setConnectionError('Failed to connect to backend server');
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setConnectionError('Backend server is not responding');
    }
  };

  // Fetch session details
  const fetchSessionDetails = async (sessionId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/status`);
      if (response.ok) {
        const data = await response.json();
        setSessionDetails(data);
        
        // Update the session in the list as well
        setSessions(prev => prev.map(session => 
          session.session_id === sessionId 
            ? { ...session, status: data.status, actions_count: data.actions.length }
            : session
        ));
      }
    } catch (error) {
      console.error('Error fetching session details:', error);
    }
  };

  // Start a new session
  const startNewSession = async () => {
    if (!newInstructions.trim()) {
      alert('Please enter instructions for the agent');
      return;
    }
    
    setIsStarting(true);
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          instructions: newInstructions,
          url: startUrl
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Session started:', data);
        setNewInstructions('');
        fetchSessions();
        setSelectedSession(data.session_id);
      } else {
        alert('Failed to start session');
      }
    } catch (error) {
      console.error('Error starting session:', error);
      alert('Error starting session: ' + error.message);
    } finally {
      setIsStarting(false);
    }
  };

  // Execute additional command
  const executeAdditionalCommand = async () => {
    if (!additionalCommand.trim() || !selectedSession) return;
    
    setIsExecuting(true);
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${selectedSession}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          command: additionalCommand
        })
      });
      
      if (response.ok) {
        setAdditionalCommand('');
        fetchSessionDetails(selectedSession);
      } else {
        alert('Failed to execute command');
      }
    } catch (error) {
      console.error('Error executing command:', error);
      alert('Error executing command: ' + error.message);
    } finally {
      setIsExecuting(false);
    }
  };

  // Stop a session
  const stopSession = async (sessionId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        fetchSessions();
        if (selectedSession === sessionId) {
          setSelectedSession(null);
          setSessionDetails(null);
        }
      }
    } catch (error) {
      console.error('Error stopping session:', error);
    }
  };

  // Setup WebSocket connection for real-time updates
  const setupWebSocket = (sessionId) => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    try {
      const ws = new WebSocket(`ws://localhost:8000/ws/${sessionId}`);
      
      ws.onopen = () => {
        console.log('WebSocket connected for session:', sessionId);
      };
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'actions_update' || data.type === 'status_update') {
          fetchSessionDetails(sessionId);
        }
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        // Fall back to polling if WebSocket fails
        startPolling(sessionId);
      };
      
      ws.onclose = () => {
        console.log('WebSocket closed');
      };
      
      wsRef.current = ws;
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      // Fall back to polling
      startPolling(sessionId);
    }
  };

  // Fallback polling for updates
  const startPolling = (sessionId) => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }
    
    pollingRef.current = setInterval(() => {
      fetchSessionDetails(sessionId);
    }, 2000);
  };

  // Effects
  useEffect(() => {
    fetchSessions();
    const interval = setInterval(fetchSessions, 5000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (selectedSession) {
      fetchSessionDetails(selectedSession);
      setupWebSocket(selectedSession);
    }
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [selectedSession]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing': return <Loader className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'initializing': return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <Monitor className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'initializing': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2 flex items-center gap-3">
            <Monitor className="w-10 h-10 text-indigo-600" />
            Autonomous Browser Agent
          </h1>
          <p className="text-gray-600 text-lg">Control and monitor AI-powered browser automation</p>
          {connectionError && (
            <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-700">{connectionError}</span>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Panel - New Session */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Play className="w-5 h-5 text-indigo-600" />
                Start New Session
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Starting URL
                  </label>
                  <input
                    type="url"
                    value={startUrl}
                    onChange={(e) => setStartUrl(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="https://example.com"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instructions for Agent
                  </label>
                  <textarea
                    value={newInstructions}
                    onChange={(e) => setNewInstructions(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent h-32 resize-none"
                    placeholder="e.g., 'Go to Google and search for Python tutorials'"
                  />
                </div>
                
                <button
                  onClick={startNewSession}
                  disabled={isStarting || !newInstructions.trim()}
                  className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 transition-colors"
                >
                  {isStarting ? (
                    <>
                      <Loader className="w-4 h-4 animate-spin" />
                      Starting...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      Start Agent
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Sessions List */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                  <Monitor className="w-5 h-5 text-indigo-600" />
                  Active Sessions
                </h2>
                <button
                  onClick={fetchSessions}
                  className="text-indigo-600 hover:text-indigo-800 p-1 rounded"
                  title="Refresh sessions"
                >
                  <Monitor className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {sessions.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No active sessions</p>
                ) : (
                  sessions.map((session) => (
                    <div
                      key={session.session_id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedSession === session.session_id
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedSession(session.session_id)}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(session.status)}
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(session.status)}`}>
                            {session.status}
                          </span>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            stopSession(session.session_id);
                          }}
                          className="text-red-500 hover:text-red-700 p-1"
                          title="Stop session"
                        >
                          <Square className="w-3 h-3" />
                        </button>
                      </div>
                      <p className="text-sm text-gray-600 truncate">
                        {session.instructions}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {session.actions_count} actions • {new Date(session.created_at).toLocaleTimeString()}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Session Details */}
          <div className="lg:col-span-2">
            {selectedSession ? (
              <div className="space-y-6">
                {/* Session Info */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">
                      Session Details
                    </h2>
                    {sessionDetails && (
                      <div className="flex items-center gap-2">
                        {getStatusIcon(sessionDetails.status)}
                        <span className={`text-sm px-3 py-1 rounded-full ${getStatusColor(sessionDetails.status)}`}>
                          {sessionDetails.status}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {sessionDetails && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm font-medium text-gray-700">Session ID</p>
                        <p className="text-xs text-gray-600 font-mono">{selectedSession}</p>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm font-medium text-gray-700">Created</p>
                        <p className="text-xs text-gray-600">
                          {new Date(sessionDetails.created_at).toLocaleString()}
                        </p>
                      </div>
                      {sessionDetails.current_state?.url && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-gray-700">Current URL</p>
                          <p className="text-xs text-gray-600 truncate">{sessionDetails.current_state.url}</p>
                        </div>
                      )}
                      {sessionDetails.current_state?.title && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-gray-700">Page Title</p>
                          <p className="text-xs text-gray-600 truncate">{sessionDetails.current_state.title}</p>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Additional Command Input */}
                  <div className="border-t pt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Execute Additional Command
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={additionalCommand}
                        onChange={(e) => setAdditionalCommand(e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="e.g., 'Click on the first search result'"
                        onKeyPress={(e) => e.key === 'Enter' && executeAdditionalCommand()}
                      />
                      <button
                        onClick={executeAdditionalCommand}
                        disabled={isExecuting || !additionalCommand.trim()}
                        className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
                      >
                        {isExecuting ? (
                          <Loader className="w-4 h-4 animate-spin" />
                        ) : (
                          <Send className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Actions Log */}
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Actions Log
                  </h3>
                  
                  <div className="max-h-96 overflow-y-auto space-y-3">
                    {sessionDetails?.actions && sessionDetails.actions.length > 0 ? (
                      sessionDetails.actions.map((action, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border-l-4 ${
                            action.error
                              ? 'border-red-500 bg-red-50'
                              : action.type === 'complete'
                              ? 'border-green-500 bg-green-50'
                              : 'border-blue-500 bg-blue-50'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium text-gray-800 capitalize">
                              {action.type}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(action.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700">{action.description}</p>
                          {action.error && (
                            <p className="text-xs text-red-600 mt-1">Error: {action.error}</p>
                          )}
                          {action.result && (
                            <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-600 max-h-20 overflow-y-auto">
                              <pre className="whitespace-pre-wrap">
                                {typeof action.result === 'object' 
                                  ? JSON.stringify(action.result, null, 2)
                                  : action.result
                                }
                              </pre>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-8">No actions recorded yet</p>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-lg p-12 text-center">
                <Monitor className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  No Session Selected
                </h3>
                <p className="text-gray-600">
                  Start a new session or select an existing one to view details and control the browser agent.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrowserAgentDashboard;