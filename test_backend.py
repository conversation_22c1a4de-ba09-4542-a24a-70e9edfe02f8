#!/usr/bin/env python3
"""
Test script to verify the backend is working correctly
"""

import requests
import json
import time

API_BASE_URL = 'http://localhost:8000/api'

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check successful: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_sessions_endpoint():
    """Test the sessions endpoint"""
    print("\nTesting sessions endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/sessions")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Sessions endpoint successful: {len(data.get('sessions', []))} sessions")
            return True
        else:
            print(f"❌ Sessions endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Sessions endpoint error: {e}")
        return False

def test_create_session():
    """Test creating a new session"""
    print("\nTesting session creation...")
    try:
        session_data = {
            "instructions": "Test session - navigate to google.com",
            "url": "https://google.com"
        }
        response = requests.post(
            f"{API_BASE_URL}/sessions/start",
            json=session_data,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            session_id = data.get('session_id')
            print(f"✅ Session created successfully: {session_id}")
            return session_id
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Session creation error: {e}")
        return None

def test_session_status(session_id):
    """Test getting session status"""
    print(f"\nTesting session status for {session_id}...")
    try:
        response = requests.get(f"{API_BASE_URL}/sessions/{session_id}/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session status retrieved: {data.get('status')}")
            print(f"   Actions: {len(data.get('actions', []))}")
            return True
        else:
            print(f"❌ Session status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Session status error: {e}")
        return False

def test_execute_command(session_id):
    """Test executing a command"""
    print(f"\nTesting command execution for {session_id}...")
    try:
        command_data = {
            "session_id": session_id,
            "command": "Take a screenshot"
        }
        response = requests.post(
            f"{API_BASE_URL}/sessions/{session_id}/execute",
            json=command_data,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Command executed: {data.get('status')}")
            return True
        else:
            print(f"❌ Command execution failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Command execution error: {e}")
        return False

def test_stop_session(session_id):
    """Test stopping a session"""
    print(f"\nTesting session stop for {session_id}...")
    try:
        response = requests.delete(f"{API_BASE_URL}/sessions/{session_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session stopped: {data.get('status')}")
            return True
        else:
            print(f"❌ Session stop failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Session stop error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Backend API")
    print("=" * 50)
    
    # Test health check
    if not test_health_check():
        print("\n❌ Backend is not responding. Make sure it's running with: python backend.py")
        return
    
    # Test sessions endpoint
    if not test_sessions_endpoint():
        return
    
    # Test session creation
    session_id = test_create_session()
    if not session_id:
        return
    
    # Wait a bit for session to initialize
    print("\nWaiting 3 seconds for session to initialize...")
    time.sleep(3)
    
    # Test session status
    if not test_session_status(session_id):
        return
    
    # Test command execution
    if not test_execute_command(session_id):
        return
    
    # Wait a bit for command to execute
    print("\nWaiting 3 seconds for command to execute...")
    time.sleep(3)
    
    # Check status again
    test_session_status(session_id)
    
    # Test session stop
    test_stop_session(session_id)
    
    print("\n🎉 All tests completed successfully!")
    print("\nYou can now open http://localhost:8000 in your browser to use the dashboard.")

if __name__ == "__main__":
    main()
