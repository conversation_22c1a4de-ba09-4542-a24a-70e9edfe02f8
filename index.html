<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autonomous Browser Agent Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="app"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';

        // Global state
        let state = {
            sessions: [],
            activeSessions: [],
            newInstructions: '',
            startUrl: 'https://google.com',
            selectedSession: null,
            sessionDetails: null,
            isStarting: false,
            additionalCommand: '',
            isExecuting: false,
            connectionError: ''
        };

        let wsRef = null;
        let pollingRef = null;

        // Fetch all sessions
        const fetchSessions = async () => {
            try {
                const response = await fetch(`${API_BASE_URL}/sessions`);
                if (response.ok) {
                    const data = await response.json();
                    state.sessions = data.sessions || [];
                    state.connectionError = '';
                } else {
                    state.connectionError = 'Failed to connect to backend server';
                }
            } catch (error) {
                console.error('Error fetching sessions:', error);
                state.connectionError = 'Backend server is not responding';
            }
            render();
        };

        // Fetch session details
        const fetchSessionDetails = async (sessionId) => {
            try {
                const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/status`);
                if (response.ok) {
                    const data = await response.json();
                    state.sessionDetails = data;

                    // Update the session in the list as well
                    state.sessions = state.sessions.map(session =>
                        session.session_id === sessionId
                            ? { ...session, status: data.status, actions_count: data.actions.length }
                            : session
                    );
                }
            } catch (error) {
                console.error('Error fetching session details:', error);
            }
            render();
        };

        // Start a new session
        const startNewSession = async () => {
            if (!state.newInstructions.trim()) {
                alert('Please enter instructions for the agent');
                return;
            }

            state.isStarting = true;
            render();

            try {
                const response = await fetch(`${API_BASE_URL}/sessions/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        instructions: state.newInstructions,
                        url: state.startUrl
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Session started:', data);
                    state.newInstructions = '';
                    fetchSessions();
                    state.selectedSession = data.session_id;
                } else {
                    alert('Failed to start session');
                }
            } catch (error) {
                console.error('Error starting session:', error);
                alert('Error starting session: ' + error.message);
            } finally {
                state.isStarting = false;
                render();
            }
        };

        // Execute additional command
        const executeAdditionalCommand = async () => {
            if (!state.additionalCommand.trim() || !state.selectedSession) return;

            state.isExecuting = true;
            render();

            try {
                const response = await fetch(`${API_BASE_URL}/sessions/${state.selectedSession}/execute`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: state.selectedSession,
                        command: state.additionalCommand
                    })
                });

                if (response.ok) {
                    state.additionalCommand = '';
                    fetchSessionDetails(state.selectedSession);
                } else {
                    alert('Failed to execute command');
                }
            } catch (error) {
                console.error('Error executing command:', error);
                alert('Error executing command: ' + error.message);
            } finally {
                state.isExecuting = false;
                render();
            }
        };

        // Stop a session
        const stopSession = async (sessionId) => {
            try {
                const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    fetchSessions();
                    if (state.selectedSession === sessionId) {
                        state.selectedSession = null;
                        state.sessionDetails = null;
                    }
                }
            } catch (error) {
                console.error('Error stopping session:', error);
            }
            render();
        };

        // Setup WebSocket connection for real-time updates
        const setupWebSocket = (sessionId) => {
            if (wsRef) {
                wsRef.close();
            }

            try {
                const ws = new WebSocket(`ws://localhost:8000/ws/${sessionId}`);

                ws.onopen = () => {
                    console.log('WebSocket connected for session:', sessionId);
                };

                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    if (data.type === 'actions_update' || data.type === 'status_update') {
                        fetchSessionDetails(sessionId);
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    // Fall back to polling if WebSocket fails
                    startPolling(sessionId);
                };

                ws.onclose = () => {
                    console.log('WebSocket closed');
                };

                wsRef = ws;
            } catch (error) {
                console.error('WebSocket connection failed:', error);
                // Fall back to polling
                startPolling(sessionId);
            }
        };

        // Fallback polling for updates
        const startPolling = (sessionId) => {
            if (pollingRef) {
                clearInterval(pollingRef);
            }

            pollingRef = setInterval(() => {
                fetchSessionDetails(sessionId);
            }, 2000);
        };

        const getStatusIcon = (status) => {
            switch (status) {
                case 'completed': return '<i data-lucide="check-circle" class="w-4 h-4 text-green-500"></i>';
                case 'processing': return '<i data-lucide="loader" class="w-4 h-4 text-blue-500 animate-spin"></i>';
                case 'error': return '<i data-lucide="alert-circle" class="w-4 h-4 text-red-500"></i>';
                case 'initializing': return '<i data-lucide="clock" class="w-4 h-4 text-yellow-500"></i>';
                default: return '<i data-lucide="monitor" class="w-4 h-4 text-gray-500"></i>';
            }
        };

        const getStatusColor = (status) => {
            switch (status) {
                case 'completed': return 'text-green-600 bg-green-100';
                case 'processing': return 'text-blue-600 bg-blue-100';
                case 'error': return 'text-red-600 bg-red-100';
                case 'initializing': return 'text-yellow-600 bg-yellow-100';
                default: return 'text-gray-600 bg-gray-100';
            }
        };

        // Event handlers
        const handleSessionSelect = (sessionId) => {
            state.selectedSession = sessionId;
            fetchSessionDetails(sessionId);
            setupWebSocket(sessionId);
        };

        const handleInputChange = (field, value) => {
            state[field] = value;
            render();
        };

        const handleKeyPress = (event, callback) => {
            if (event.key === 'Enter') {
                callback();
            }
        };

        // Render function
        const render = () => {
            const app = document.getElementById('app');
            app.innerHTML = `
                <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
                    <div class="max-w-7xl mx-auto">
                        <!-- Header -->
                        <div class="mb-8">
                            <h1 class="text-4xl font-bold text-gray-900 mb-2 flex items-center gap-3">
                                <i data-lucide="monitor" class="w-10 h-10 text-indigo-600"></i>
                                Autonomous Browser Agent
                            </h1>
                            <p class="text-gray-600 text-lg">Control and monitor AI-powered browser automation</p>
                            ${state.connectionError ? `
                                <div class="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg flex items-center gap-2">
                                    <i data-lucide="alert-circle" class="w-5 h-5 text-red-500"></i>
                                    <span class="text-red-700">${state.connectionError}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Left Panel - New Session -->
                            <div class="lg:col-span-1">
                                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                                    <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                        <i data-lucide="play" class="w-5 h-5 text-indigo-600"></i>
                                        Start New Session
                                    </h2>

                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Starting URL
                                            </label>
                                            <input
                                                type="url"
                                                value="${state.startUrl}"
                                                onchange="handleInputChange('startUrl', this.value)"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                placeholder="https://example.com"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Instructions for Agent
                                            </label>
                                            <textarea
                                                value="${state.newInstructions}"
                                                onchange="handleInputChange('newInstructions', this.value)"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent h-32 resize-none"
                                                placeholder="e.g., 'Go to Google and search for Python tutorials'"
                                            ></textarea>
                                        </div>

                                        <button
                                            onclick="startNewSession()"
                                            ${state.isStarting || !state.newInstructions.trim() ? 'disabled' : ''}
                                            class="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 transition-colors"
                                        >
                                            ${state.isStarting ? `
                                                <i data-lucide="loader" class="w-4 h-4 animate-spin"></i>
                                                Starting...
                                            ` : `
                                                <i data-lucide="play" class="w-4 h-4"></i>
                                                Start Agent
                                            `}
                                        </button>
                                    </div>
                                </div>

                                <!-- Sessions List -->
                                <div class="bg-white rounded-xl shadow-lg p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-2">
                                            <i data-lucide="monitor" class="w-5 h-5 text-indigo-600"></i>
                                            Active Sessions
                                        </h2>
                                        <button
                                            onclick="fetchSessions()"
                                            class="text-indigo-600 hover:text-indigo-800 p-1 rounded"
                                            title="Refresh sessions"
                                        >
                                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                        </button>
                                    </div>

                                    <div class="space-y-2 max-h-64 overflow-y-auto">
                                        ${state.sessions.length === 0 ? `
                                            <p class="text-gray-500 text-center py-4">No active sessions</p>
                                        ` : state.sessions.map(session => `
                                            <div
                                                class="p-3 rounded-lg border cursor-pointer transition-colors ${
                                                    state.selectedSession === session.session_id
                                                        ? 'border-indigo-500 bg-indigo-50'
                                                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                                }"
                                                onclick="handleSessionSelect('${session.session_id}')"
                                            >
                                                <div class="flex items-center justify-between mb-1">
                                                    <div class="flex items-center gap-2">
                                                        ${getStatusIcon(session.status)}
                                                        <span class="text-xs px-2 py-1 rounded-full ${getStatusColor(session.status)}">
                                                            ${session.status}
                                                        </span>
                                                    </div>
                                                    <button
                                                        onclick="event.stopPropagation(); stopSession('${session.session_id}')"
                                                        class="text-red-500 hover:text-red-700 p-1"
                                                        title="Stop session"
                                                    >
                                                        <i data-lucide="square" class="w-3 h-3"></i>
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-600 truncate">
                                                    ${session.instructions}
                                                </p>
                                                <p class="text-xs text-gray-400 mt-1">
                                                    ${session.actions_count || 0} actions • ${new Date(session.created_at).toLocaleTimeString()}
                                                </p>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>

                            <!-- Right Panel - Session Details -->
                            <div class="lg:col-span-2">
                                ${state.selectedSession ? renderSessionDetails() : renderNoSessionSelected()}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        };

        const renderSessionDetails = () => {
            return `
                <div class="space-y-6">
                    <!-- Session Info -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-semibold text-gray-800">
                                Session Details
                            </h2>
                            ${state.sessionDetails ? `
                                <div class="flex items-center gap-2">
                                    ${getStatusIcon(state.sessionDetails.status)}
                                    <span class="text-sm px-3 py-1 rounded-full ${getStatusColor(state.sessionDetails.status)}">
                                        ${state.sessionDetails.status}
                                    </span>
                                </div>
                            ` : ''}
                        </div>

                        ${state.sessionDetails ? `
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <p class="text-sm font-medium text-gray-700">Session ID</p>
                                    <p class="text-xs text-gray-600 font-mono">${state.selectedSession}</p>
                                </div>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <p class="text-sm font-medium text-gray-700">Created</p>
                                    <p class="text-xs text-gray-600">
                                        ${new Date(state.sessionDetails.created_at).toLocaleString()}
                                    </p>
                                </div>
                                ${state.sessionDetails.current_state?.url ? `
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-sm font-medium text-gray-700">Current URL</p>
                                        <p class="text-xs text-gray-600 truncate">${state.sessionDetails.current_state.url}</p>
                                    </div>
                                ` : ''}
                                ${state.sessionDetails.current_state?.title ? `
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-sm font-medium text-gray-700">Page Title</p>
                                        <p class="text-xs text-gray-600 truncate">${state.sessionDetails.current_state.title}</p>
                                    </div>
                                ` : ''}
                            </div>
                        ` : ''}

                        <!-- Additional Command Input -->
                        <div class="border-t pt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Execute Additional Command
                            </label>
                            <div class="flex gap-2">
                                <input
                                    type="text"
                                    value="${state.additionalCommand}"
                                    onchange="handleInputChange('additionalCommand', this.value)"
                                    onkeypress="handleKeyPress(event, executeAdditionalCommand)"
                                    class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                    placeholder="e.g., 'Click on the first search result'"
                                />
                                <button
                                    onclick="executeAdditionalCommand()"
                                    ${state.isExecuting || !state.additionalCommand.trim() ? 'disabled' : ''}
                                    class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
                                >
                                    ${state.isExecuting ? `
                                        <i data-lucide="loader" class="w-4 h-4 animate-spin"></i>
                                    ` : `
                                        <i data-lucide="send" class="w-4 h-4"></i>
                                    `}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Log -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            Actions Log
                        </h3>

                        <div class="max-h-96 overflow-y-auto space-y-3">
                            ${state.sessionDetails?.actions && state.sessionDetails.actions.length > 0 ?
                                state.sessionDetails.actions.map((action, index) => `
                                    <div
                                        class="p-3 rounded-lg border-l-4 ${
                                            action.error
                                                ? 'border-red-500 bg-red-50'
                                                : action.type === 'complete'
                                                ? 'border-green-500 bg-green-50'
                                                : 'border-blue-500 bg-blue-50'
                                        }"
                                    >
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium text-gray-800 capitalize">
                                                ${action.type}
                                            </span>
                                            <span class="text-xs text-gray-500">
                                                ${new Date(action.timestamp).toLocaleTimeString()}
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-700">${action.description}</p>
                                        ${action.error ? `
                                            <p class="text-xs text-red-600 mt-1">Error: ${action.error}</p>
                                        ` : ''}
                                        ${action.result ? `
                                            <div class="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-600 max-h-20 overflow-y-auto">
                                                <pre class="whitespace-pre-wrap">${
                                                    typeof action.result === 'object'
                                                        ? JSON.stringify(action.result, null, 2)
                                                        : action.result
                                                }</pre>
                                            </div>
                                        ` : ''}
                                    </div>
                                `).join('')
                                : '<p class="text-gray-500 text-center py-8">No actions recorded yet</p>'
                            }
                        </div>
                    </div>
                </div>
            `;
        };

        const renderNoSessionSelected = () => {
            return `
                <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                    <i data-lucide="monitor" class="w-16 h-16 text-gray-300 mx-auto mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">
                        No Session Selected
                    </h3>
                    <p class="text-gray-600">
                        Start a new session or select an existing one to view details and control the browser agent.
                    </p>
                </div>
            `;
        };

        // Initialize the app
        document.addEventListener('DOMContentLoaded', () => {
            render();
            fetchSessions();

            // Set up periodic session refresh
            setInterval(fetchSessions, 5000);
        });

        // Handle session selection changes
        document.addEventListener('change', (event) => {
            if (state.selectedSession) {
                fetchSessionDetails(state.selectedSession);
                setupWebSocket(state.selectedSession);
            }

            // Cleanup on session change
            if (wsRef) {
                wsRef.close();
            }
            if (pollingRef) {
                clearInterval(pollingRef);
            }
        });
    </script>
</body>
</html>
