<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autonomous Browser Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .control-panel {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
        }

        .control-panel input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }

        .control-panel input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            background: #f7fafc;
            border-left: 4px solid #667eea;
        }

        .status.connected {
            background: #f0fff4;
            border-left-color: #48bb78;
        }

        .status.error {
            background: #fed7d7;
            border-left-color: #f56565;
        }

        .logs {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e9ecef;
        }

        .log-entry {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .log-time {
            color: #6c757d;
            font-size: 0.8rem;
            min-width: 80px;
        }

        .log-type {
            font-weight: 600;
            min-width: 100px;
        }

        .log-message {
            flex: 1;
            word-break: break-word;
        }

        .log-system {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .log-instruction {
            background: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }

        .log-result {
            background: #e8f5e8;
            border-left: 3px solid #4caf50;
        }

        .log-error {
            background: #ffebee;
            border-left: 3px solid #f44336;
        }

        .log-reasoning {
            background: #fef5e7;
            border-left: 3px solid #f6ad55;
        }

        .browser-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .browser-info p {
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }

        .executing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Autonomous Browser Agent</h1>
            <p>Control navegadores web con instrucciones en lenguaje natural</p>
            <p><small>Servidor Node.js - Puerto 3000</small></p>
        </div>

        <div class="card">
            <div id="status" class="status">
                <span>🔴 Desconectado</span>
            </div>

            <div class="control-panel">
                <button id="connectBtn" class="btn btn-primary" onclick="startSession()">
                    Conectar Agente
                </button>
            </div>

            <div id="instructionPanel" class="control-panel" style="display: none;">
                <input 
                    type="text" 
                    id="instructionInput" 
                    placeholder="Ingresa una instrucción (ej: 'ir a google.com')"
                    onkeydown="handleKeyDown(event)"
                >
                <button id="sendBtn" class="btn btn-primary" onclick="sendInstruction()">
                    Enviar
                </button>
            </div>

            <div id="browserInfo" class="browser-info" style="display: none;">
                <p><strong>Session ID:</strong> <span id="sessionId">-</span></p>
                <p><strong>URL Actual:</strong> <span id="currentUrl">-</span></p>
                <p><strong>Título:</strong> <span id="pageTitle">-</span></p>
            </div>
        </div>

        <div class="card">
            <h3>📋 Registro de Actividad</h3>
            <div id="logs" class="logs">
                <div class="log-entry log-system">
                    <span class="log-time">--:--:--</span>
                    <span class="log-type">[Sistema]</span>
                    <span class="log-message">Servidor Node.js iniciado - Listo para conectar...</span>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
