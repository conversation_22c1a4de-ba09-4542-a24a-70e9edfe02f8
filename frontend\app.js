// Autonomous Browser Agent - Frontend JavaScript
let ws = null;
let sessionId = null;
let connected = false;
let isExecuting = false;

function addLog(type, message) {
    const logs = document.getElementById('logs');
    const time = new Date().toLocaleTimeString();
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type.toLowerCase()}`;
    logEntry.innerHTML = `
        <span class="log-time">${time}</span>
        <span class="log-type">[${type}]</span>
        <span class="log-message">${message}</span>
    `;
    
    logs.appendChild(logEntry);
    logs.scrollTop = logs.scrollHeight;
}

function updateStatus(status, isConnected = false, isError = false) {
    const statusEl = document.getElementById('status');
    statusEl.textContent = status;
    statusEl.className = 'status';
    if (isConnected) statusEl.classList.add('connected');
    if (isError) statusEl.classList.add('error');
}

async function startSession() {
    try {
        updateStatus('🔄 Conectando...');
        document.getElementById('connectBtn').disabled = true;
        
        addLog('Sistema', 'Conectando al backend a través del proxy...');
        
        // Usar el proxy del servidor Node.js
        const response = await fetch('/api/start-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Error del servidor: ${response.status} - ${errorText}`);
        }
        
        const data = await response.json();
        sessionId = data.session_id;
        
        document.getElementById('sessionId').textContent = sessionId;
        document.getElementById('browserInfo').style.display = 'block';
        
        addLog('Sistema', `✅ Sesión creada: ${sessionId}`);
        connectWebSocket(sessionId);
        
    } catch (error) {
        updateStatus('❌ Error de conexión', false, true);
        addLog('Error', `❌ ${error.message}`);
        document.getElementById('connectBtn').disabled = false;
    }
}

function connectWebSocket(id) {
    addLog('Sistema', 'Conectando WebSocket...');
    
    try {
        // Usar la URL del servidor actual para WebSocket
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        const wsUrl = `${protocol}//${host}:8000/ws/${id}`;
        
        addLog('Sistema', `Conectando a: ${wsUrl}`);
        ws = new WebSocket(wsUrl);
        
        ws.onopen = () => {
            connected = true;
            updateStatus('🟢 Conectado', true);
            addLog('Sistema', '✅ WebSocket conectado');
            
            document.getElementById('connectBtn').style.display = 'none';
            document.getElementById('instructionPanel').style.display = 'flex';
        };
        
        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.type === 'state_update') {
                    const state = data.data;
                    if (state.url) {
                        document.getElementById('currentUrl').textContent = state.url;
                    }
                    if (state.title) {
                        document.getElementById('pageTitle').textContent = state.title;
                    }
                } else if (data.type === 'instruction_result') {
                    isExecuting = false;
                    updateSendButton();
                    const result = data.data;
                    if (result.status === 'success') {
                        addLog('Resultado', `✅ ${result.message || 'Instrucción completada'}`);
                    } else {
                        addLog('Error', `❌ ${result.message || 'Instrucción falló'}`);
                    }
                } else if (data.type === 'reasoning_step') {
                    addLog('Razonamiento', `🤔 ${data.data.message}`);
                } else if (data.type === 'instruction_received') {
                    isExecuting = true;
                    updateSendButton();
                    addLog('Sistema', `📝 Procesando: ${data.data.instruction}`);
                } else if (data.type === 'connection_established') {
                    addLog('Sistema', '🔗 Conexión del navegador establecida');
                } else if (data.type === 'error') {
                    isExecuting = false;
                    updateSendButton();
                    addLog('Error', `❌ ${data.message}`);
                }
            } catch (parseError) {
                addLog('Error', `❌ Error parsing WebSocket message: ${parseError.message}`);
            }
        };
        
        ws.onclose = (event) => {
            connected = false;
            updateStatus('🔴 Desconectado');
            addLog('Sistema', `WebSocket desconectado (código: ${event.code})`);
        };
        
        ws.onerror = (error) => {
            updateStatus('❌ Error de WebSocket', false, true);
            addLog('Error', `❌ Error de WebSocket`);
        };
        
    } catch (wsError) {
        addLog('Error', `❌ Error creando WebSocket: ${wsError.message}`);
        updateStatus('❌ Error de conexión', false, true);
        document.getElementById('connectBtn').disabled = false;
    }
}

function sendInstruction() {
    const input = document.getElementById('instructionInput');
    const instruction = input.value.trim();
    
    if (!ws || !instruction || isExecuting) return;
    
    ws.send(JSON.stringify({ command: instruction }));
    addLog('Instrucción', `🚀 ${instruction}`);
    input.value = '';
}

function updateSendButton() {
    const btn = document.getElementById('sendBtn');
    const input = document.getElementById('instructionInput');
    
    if (isExecuting) {
        btn.textContent = 'Ejecutando...';
        btn.disabled = true;
        input.disabled = true;
        btn.classList.add('executing');
    } else {
        btn.textContent = 'Enviar';
        btn.disabled = false;
        input.disabled = false;
        btn.classList.remove('executing');
    }
}

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendInstruction();
    }
}

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    addLog('Sistema', 'Aplicación iniciada');
    addLog('Sistema', 'Ejemplos de comandos:');
    addLog('Sistema', '• "ir a google.com"');
    addLog('Sistema', '• "hacer clic en buscar"');
    addLog('Sistema', '• "escribir hola mundo en el campo de búsqueda"');
    addLog('Sistema', '• "desplazarse hacia abajo"');
});
