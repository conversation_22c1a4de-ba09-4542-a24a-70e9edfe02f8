#!/usr/bin/env python3
"""
Startup script for the Browser Agent Dashboard
This script starts the backend server and provides helpful information
"""

import subprocess
import sys
import time
import webbrowser
import requests
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['fastapi', 'uvicorn', 'selenium', 'requests', 'websockets']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall them with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed")
    return True

def wait_for_server(url, timeout=30):
    """Wait for the server to be ready"""
    print(f"Waiting for server to start at {url}...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/api/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ Server did not start within {timeout} seconds")
    return False

def main():
    """Main startup function"""
    print("🚀 Starting Browser Agent Dashboard")
    print("=" * 50)
    
    # Check if backend.py exists
    if not Path("backend.py").exists():
        print("❌ backend.py not found in current directory")
        print("Make sure you're running this from the project root directory")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    print("\n📦 Starting backend server...")
    
    try:
        # Start the backend server
        process = subprocess.Popen(
            [sys.executable, "backend.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Wait a moment for the server to start
        time.sleep(2)
        
        # Check if server is ready
        server_url = "http://localhost:8000"
        if wait_for_server(server_url):
            print(f"\n🎉 Dashboard is ready!")
            print(f"📱 Frontend: {server_url}")
            print(f"📚 API Docs: {server_url}/docs")
            print(f"🔍 Health Check: {server_url}/api/health")
            
            # Open browser
            print(f"\n🌐 Opening browser...")
            webbrowser.open(server_url)
            
            print(f"\n📋 Available endpoints:")
            print(f"   GET  /api/health - Health check")
            print(f"   GET  /api/sessions - List sessions")
            print(f"   POST /api/sessions/start - Start new session")
            print(f"   GET  /api/sessions/{{id}}/status - Get session status")
            print(f"   POST /api/sessions/{{id}}/execute - Execute command")
            print(f"   DELETE /api/sessions/{{id}} - Stop session")
            print(f"   WS   /ws/{{id}} - WebSocket connection")
            
            print(f"\n🛑 Press Ctrl+C to stop the server")
            
            # Keep the script running and show server output
            try:
                while True:
                    output = process.stdout.readline()
                    if output:
                        print(output.strip())
                    elif process.poll() is not None:
                        break
            except KeyboardInterrupt:
                print(f"\n🛑 Stopping server...")
                process.terminate()
                process.wait()
                print("✅ Server stopped")
        else:
            print("❌ Failed to start server")
            process.terminate()
            return 1
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
