/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.App-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  text-align: center;
}

.App-header h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Main Content */
main {
  flex: 1;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto 1fr;
  gap: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Control Panel */
.control-panel {
  grid-column: 1 / -1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.connect-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.connect-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.connect-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.instruction-panel {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.instruction-panel input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.instruction-panel input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.instruction-panel button {
  background: #667eea;
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.instruction-panel button:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.instruction-panel button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Status Panel */
.status-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.connection-status {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background: #f7fafc;
  border-left: 4px solid #667eea;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.executing-indicator {
  color: #f59e0b;
  font-size: 0.9rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.session-info {
  background: #e6fffa;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border-left: 4px solid #38b2ac;
}

.session-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #234e52;
}

.reasoning-info {
  background: #fef5e7;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 4px solid #f6ad55;
}

.reasoning-info h4 {
  margin: 0 0 0.5rem 0;
  color: #744210;
  font-size: 1rem;
}

.reasoning-steps {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.reasoning-step {
  background: rgba(246, 173, 85, 0.1);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #744210;
}

.browser-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.browser-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #495057;
}

/* Logs Panel */
.logs-panel {
  grid-column: 1 / -1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}

.logs-panel h3 {
  margin-bottom: 1rem;
  color: #2d3748;
  font-size: 1.3rem;
}

.logs {
  flex: 1;
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.log-entry {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.log-time {
  color: #6c757d;
  font-size: 0.8rem;
  min-width: 80px;
}

.log-type {
  font-weight: 600;
  min-width: 80px;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

/* Log Type Colors */
.log-system {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.log-instruction {
  background: #f3e5f5;
  border-left: 3px solid #9c27b0;
}

.log-result {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.log-error {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

.log-reasoning {
  background: #fef5e7;
  border-left: 3px solid #f6ad55;
}

.log-system .log-type { color: #2196f3; }
.log-instruction .log-type { color: #9c27b0; }
.log-result .log-type { color: #4caf50; }
.log-error .log-type { color: #f44336; }
.log-reasoning .log-type { color: #f6ad55; }

/* Responsive Design */
@media (max-width: 768px) {
  main {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .instruction-panel {
    flex-direction: column;
  }

  .instruction-panel input {
    width: 100%;
  }
}

/* Scrollbar Styling */
.logs::-webkit-scrollbar {
  width: 8px;
}

.logs::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.logs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.logs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
