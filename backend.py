#!/usr/bin/env python3
"""
Simple FastAPI backend example for the Browser Agent Dashboard
This demonstrates how to use the installed dependencies: fastapi, uvicorn, selenium, requests, websockets
"""

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import Dict, List, Optional
import asyncio
import json
import uuid
from datetime import datetime
import uvicorn

# Data models
class SessionStart(BaseModel):
    instructions: str
    url: str

class CommandExecute(BaseModel):
    session_id: str
    command: str

class SessionInfo(BaseModel):
    session_id: str
    instructions: str
    status: str
    created_at: datetime
    actions_count: int = 0

class ActionLog(BaseModel):
    type: str
    description: str
    timestamp: datetime
    error: Optional[str] = None
    result: Optional[dict] = None

class SessionDetails(BaseModel):
    session_id: str
    instructions: str
    status: str
    created_at: datetime
    current_state: Optional[dict] = None
    actions: List[ActionLog] = []

# Initialize FastAPI app
app = FastAPI(title="Browser Agent API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage (in production, use a proper database)
sessions: Dict[str, SessionDetails] = {}
websocket_connections: Dict[str, WebSocket] = {}

# API Routes
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(sessions),
        "websocket_connections": len(websocket_connections)
    }

@app.get("/api/sessions")
async def get_sessions():
    """Get all active sessions"""
    session_list = [
        SessionInfo(
            session_id=session_id,
            instructions=session.instructions,
            status=session.status,
            created_at=session.created_at,
            actions_count=len(session.actions)
        )
        for session_id, session in sessions.items()
    ]
    return {"sessions": session_list}

@app.post("/api/sessions/start")
async def start_session(session_data: SessionStart):
    """Start a new browser automation session"""
    session_id = str(uuid.uuid4())

    # Create new session
    session = SessionDetails(
        session_id=session_id,
        instructions=session_data.instructions,
        status="initializing",
        created_at=datetime.now(),
        current_state={"url": session_data.url},
        actions=[]
    )

    sessions[session_id] = session

    # Simulate session initialization
    asyncio.create_task(simulate_session_execution(session_id))

    return {"session_id": session_id, "status": "started"}

@app.get("/api/sessions/{session_id}/status")
async def get_session_status(session_id: str):
    """Get detailed status of a specific session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    return sessions[session_id]

@app.post("/api/sessions/{session_id}/execute")
async def execute_command(session_id: str, command_data: CommandExecute):
    """Execute an additional command in an existing session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = sessions[session_id]

    # Add command to actions log
    action = ActionLog(
        type="command",
        description=f"Executing: {command_data.command}",
        timestamp=datetime.now()
    )
    session.actions.append(action)
    session.status = "processing"

    # Simulate command execution
    asyncio.create_task(simulate_command_execution(session_id, command_data.command))

    return {"status": "command_queued"}

@app.delete("/api/sessions/{session_id}")
async def stop_session(session_id: str):
    """Stop and remove a session"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    # Close WebSocket connection if exists
    if session_id in websocket_connections:
        await websocket_connections[session_id].close()
        del websocket_connections[session_id]

    # Remove session
    del sessions[session_id]

    return {"status": "session_stopped"}

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time session updates"""
    await websocket.accept()
    websocket_connections[session_id] = websocket

    try:
        while True:
            # Keep connection alive and listen for messages
            await websocket.receive_text()
    except WebSocketDisconnect:
        if session_id in websocket_connections:
            del websocket_connections[session_id]

# Simulation functions (replace with actual Selenium automation)
async def simulate_session_execution(session_id: str):
    """Simulate browser automation execution"""
    if session_id not in sessions:
        return

    session = sessions[session_id]

    # Simulate initialization
    await asyncio.sleep(2)
    session.status = "processing"
    session.actions.append(ActionLog(
        type="navigate",
        description=f"Navigating to {session.current_state['url']}",
        timestamp=datetime.now(),
        result={"success": True}
    ))
    await notify_websocket(session_id, {"type": "status_update"})

    # Simulate some actions
    await asyncio.sleep(3)
    session.actions.append(ActionLog(
        type="action",
        description="Performing automated actions based on instructions",
        timestamp=datetime.now(),
        result={"actions_performed": 3}
    ))
    await notify_websocket(session_id, {"type": "actions_update"})

    # Complete session
    await asyncio.sleep(2)
    session.status = "completed"
    session.actions.append(ActionLog(
        type="complete",
        description="Session completed successfully",
        timestamp=datetime.now(),
        result={"final_status": "success"}
    ))
    await notify_websocket(session_id, {"type": "status_update"})

async def simulate_command_execution(session_id: str, command: str):
    """Simulate execution of an additional command"""
    if session_id not in sessions:
        return

    session = sessions[session_id]

    await asyncio.sleep(2)

    # Simulate command result
    session.actions.append(ActionLog(
        type="command_result",
        description=f"Completed: {command}",
        timestamp=datetime.now(),
        result={"command": command, "success": True}
    ))

    session.status = "completed"
    await notify_websocket(session_id, {"type": "actions_update"})

async def notify_websocket(session_id: str, message: dict):
    """Send notification to WebSocket client"""
    if session_id in websocket_connections:
        try:
            await websocket_connections[session_id].send_text(json.dumps(message))
        except:
            # Connection might be closed
            if session_id in websocket_connections:
                del websocket_connections[session_id]

# Serve static files (the HTML frontend)
app.mount("/", StaticFiles(directory=".", html=True), name="static")

if __name__ == "__main__":
    print("Starting Browser Agent Backend...")
    print("Frontend will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
