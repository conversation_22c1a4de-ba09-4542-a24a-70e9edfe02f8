import React, { useState, useEffect, useRef } from 'react';
import './App.css';

function App() {
  const [sessionId, setSessionId] = useState(null);
  const [connected, setConnected] = useState(false);
  const [instruction, setInstruction] = useState('');
  const [logs, setLogs] = useState([]);
  const [browserState, setBrowserState] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [reasoningSteps, setReasoningSteps] = useState([]);

  const wsRef = useRef(null);
  const logsEndRef = useRef(null);

  // Auto-scroll logs
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  const startSession = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/start-session', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to start browser session');
      }

      const data = await response.json();
      setSessionId(data.session_id);
      connectWebSocket(data.session_id);
      addLog('System', `Session started with ID: ${data.session_id}`);
    } catch (err) {
      setError(err.message);
      addLog('Error', err.message);
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = (id) => {
    const ws = new WebSocket(`ws://localhost:8000/ws/${id}`);

    ws.onopen = () => {
      setConnected(true);
      addLog('System', 'WebSocket connected');
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === 'state_update') {
        setBrowserState(data.data);
      } else if (data.type === 'instruction_result') {
        setIsExecuting(false);
        const result = data.data;
        if (result.status === 'success') {
          addLog('Result', `✅ ${result.message || 'Instruction completed successfully'}`);
        } else {
          addLog('Error', `❌ ${result.message || 'Instruction failed'}`);
        }
      } else if (data.type === 'reasoning_step') {
        const step = data.data;
        setReasoningSteps(prev => [...prev, step]);
        addLog('Reasoning', `🤔 ${step.message}`);
      } else if (data.type === 'instruction_received') {
        setIsExecuting(true);
        addLog('System', `📝 Processing: ${data.data.instruction}`);
      } else if (data.type === 'connection_established') {
        addLog('System', '🔗 Browser connection established');
      } else if (data.type === 'error') {
        setIsExecuting(false);
        addLog('Error', `❌ ${data.message}`);
      }
    };

    ws.onclose = () => {
      setConnected(false);
      addLog('System', 'WebSocket disconnected');
    };

    ws.onerror = (error) => {
      setError('WebSocket error');
      addLog('Error', 'WebSocket error');
    };

    wsRef.current = ws;
  };

  const sendInstruction = () => {
    if (!wsRef.current || !instruction.trim() || isExecuting) return;

    setReasoningSteps([]);
    wsRef.current.send(JSON.stringify({ command: instruction }));
    addLog('Instruction', `🚀 ${instruction}`);
    setInstruction('');
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendInstruction();
    }
  };

  const addLog = (type, message) => {
    setLogs(prev => [...prev, { type, message, timestamp: new Date().toISOString() }]);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Autonomous Browser Agent</h1>
      </header>

      <main>
        <div className="control-panel">
          {!sessionId ? (
            <button
              onClick={startSession}
              disabled={loading}
              className="connect-button"
            >
              {loading ? 'Connecting...' : 'Connect Agent'}
            </button>
          ) : (
            <div className="instruction-panel">
              <input
                type="text"
                value={instruction}
                onChange={(e) => setInstruction(e.target.value)}
                placeholder="Enter instruction (e.g., 'go to google.com')"
                disabled={!connected || isExecuting}
                onKeyDown={handleKeyDown}
              />
              <button
                onClick={sendInstruction}
                disabled={!connected || !instruction.trim() || isExecuting}
              >
                {isExecuting ? 'Executing...' : 'Send'}
              </button>
            </div>
          )}
        </div>

        <div className="status-panel">
          <div className="connection-status">
            Status: {connected ? '🟢 Connected' : '🔴 Disconnected'}
            {isExecuting && <span className="executing-indicator"> • Executing...</span>}
          </div>

          {sessionId && (
            <div className="session-info">
              <p><strong>Session ID:</strong> {sessionId}</p>
            </div>
          )}

          {browserState.url && (
            <div className="browser-info">
              <p><strong>Current URL:</strong> {browserState.url}</p>
              {browserState.title && <p><strong>Page Title:</strong> {browserState.title}</p>}
              <p><strong>Browser Status:</strong> {browserState.status || 'Active'}</p>
            </div>
          )}

          {reasoningSteps.length > 0 && (
            <div className="reasoning-info">
              <h4>Current Reasoning:</h4>
              <div className="reasoning-steps">
                {reasoningSteps.slice(-3).map((step, index) => (
                  <div key={index} className="reasoning-step">
                    {step.message}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="logs-panel">
          <h3>Activity Log</h3>
          <div className="logs">
            {logs.map((log, index) => (
              <div key={index} className={`log-entry log-${log.type.toLowerCase()}`}>
                <span className="log-time">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
                <span className="log-type">[{log.type}]</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;